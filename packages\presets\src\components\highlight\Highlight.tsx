import React from "react";

const Highlight: React.FC<{
  text: string;
  keyword: string;
}> = (props) => {
  const { text, keyword } = props;
  const safeKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  const parts = keyword && keyword.trim() ? text.split(new RegExp(`(${safeKeyword})`, "gi")) : [text];

  return (
    <span>
      {parts.map((part, i) => (
        <span key={i} className={part.toLowerCase() === keyword.toLowerCase() ? "pts:text-primary" : ""}>
          {part}
        </span>
      ))}
    </span>
  );
};

export default Highlight;
