import React, { useMemo } from "react";

import Highlight from "../../components/highlight/Highlight";

interface ContentProps {
  keyword: string;
  content: string;
}

const Content: React.FC<ContentProps> = (props) => {
  const { keyword, content } = props;
  const prefixLength = 20;
  const suffixLength = 150;

  const fragment = useMemo(() => {
    const safeKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    const regex = new RegExp(safeKeyword, "i");
    const firstMatchPosition = content.search(regex) ?? 0;
    const start = firstMatchPosition > prefixLength ? firstMatchPosition - prefixLength : 0;
    const end = start + safeKeyword.length + suffixLength;
    const prefix = start > 0 ? "..." : "";
    const suffix = end < content.length ? "..." : "";
    return prefix + content.slice(start, end) + suffix;
  }, [content, keyword]);

  return (
    <div
      className="pts:text-sm"
      style={
        {
          "word-break": "break-all",
          "text-overflow": "ellipsis",
          display: "-webkit-box",
          "-webkit-box-orient": "vertical",
          /* 这里是超出几行省略 */
          "-webkit-line-clamp": "2",
          overflow: "hidden",
        } as any
      }
    >
      <Highlight text={fragment} keyword={keyword} />
    </div>
  );
};

export default Content;
