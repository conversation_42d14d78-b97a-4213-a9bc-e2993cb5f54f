/**
 * 给 Tailwind CSS 类名添加前缀
 *
 * @param classNames - 要处理的类名字符串数组，每个元素可以包含多个用空格分隔的类名
 * @param prefix - 可选的前缀，如 'hover', 'md', 'dark' 等
 * @returns 处理后的类名字符串
 *
 * @example
 * ```typescript
 * cn(['flex items-center', 'text-blue-500'], 'hover') // 'hover:flex hover:items-center hover:text-blue-500'
 * cn(['w-[100px]', 'md:w-auto']) // 'w-[100px] md:w-auto' (no prefix)
 * cn(['text-blue-500', null, 'font-bold'], 'dark') // 'dark:text-blue-500 dark:font-bold'
 * ```
 */

export function createClassName(prefix: string) {
  return (...classNames: any[]) => className(classNames, prefix);
}

export function className(classNames: (string | null | undefined)[], prefix?: string | null): string {
  // 处理边界情况
  if (!Array.isArray(classNames) || classNames.length === 0) {
    return "";
  }

  // 过滤并规范化所有类名
  const validClassNames = classNames
    .filter((className): className is string => typeof className === "string" && className.trim() !== "")
    .map((className) => normalizeWhitespace(className))
    .filter(Boolean);

  if (validClassNames.length === 0) {
    return "";
  }

  // 如果没有前缀，直接合并所有类名
  if (!prefix || typeof prefix !== "string") {
    return validClassNames.join(" ");
  }

  // 处理每个类名字符串，为其中的每个类添加前缀
  const processedClassNames = validClassNames
    .map((classNameString) => {
      return classNameString
        .split(" ")
        .map((name) => {
          // 跳过空字符串
          if (!name) return "";

          // 如果类名已经有前缀（包含冒号），不重复添加
          if (name.includes(":")) {
            return name;
          }

          return `${prefix}:${name}`;
        })
        .filter(Boolean)
        .join(" ");
    })
    .filter(Boolean);

  return processedClassNames.join(" ");
}

/**
 * 规范化空白字符
 * @param str - 输入字符串
 * @returns 规范化后的字符串
 */
function normalizeWhitespace(str: string): string {
  return str.trim().replace(/\s+/g, " "); // 将多个连续空白字符替换为单个空格
}

/**
 * 合并多个类名字符串，自动去重和规范化
 * @param classNames - 类名字符串数组
 * @returns 合并后的类名字符串
 *
 * @example
 * ```typescript
 * mergeClassNames('flex items-center', 'flex justify-center', 'text-blue-500')
 * // 'flex items-center justify-center text-blue-500'
 * ```
 */
export function mergeClassNames(...classNames: (string | null | undefined)[]): string {
  const uniqueClasses = new Set<string>();

  classNames.forEach((className) => {
    if (className && typeof className === "string") {
      normalizeWhitespace(className)
        .split(" ")
        .forEach((cls) => {
          if (cls) uniqueClasses.add(cls);
        });
    }
  });

  return Array.from(uniqueClasses).join(" ");
}
