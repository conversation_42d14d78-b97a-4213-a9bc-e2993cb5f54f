import React from "react";

interface FileListItem {
  id: string;
  name: string;
  status: FileUploadStatus;
  progress: number;
}

enum FileUploadStatus {
  UPLOADING = "UPLOADING",
  UPLOADED = "UPLOADED",
  PROCESSING = "PROCESSING",
  READY = "READY",
  ERROR = "ERROR",
}

const RAGFileList: React.FC<{ data: FileListItem[] }> = (props) => {
  const { data = [] } = props;
  return (
    <div className="pts:bg-gray-50 pts:mb-4 pts:rounded-md pts:w-3/5">
      {data.map((i) => {
        const status = i.status;
        const progress = (i.progress * 100).toFixed(2);

        return (
          <div
            key={i.id}
            className="pts:flex pts:justify-between pts:items-center pts:px-4 pts:py-2 pts:border-gray-200 pts:not-last:border-b pts:text-sm"
          >
            <div className="pts:flex-1 pts:overflow-ellipsis pts:overflow-hidden pts:whitespace-nowrap">{i.name}</div>
            <div className="pts:text-xs">
              {status === FileUploadStatus.UPLOADING && <div className="pts:text-black-25">上传中... {progress}%</div>}
              {status === FileUploadStatus.UPLOADED && <div className="pts:text-black-25">上传完成</div>}
              {status === FileUploadStatus.PROCESSING && (
                <div className="pts:text-black-45 pts:text-sm">处理中... {progress}%</div>
              )}
              {status === FileUploadStatus.READY && <div className="pts:text-black-25">完成</div>}
              {status === FileUploadStatus.ERROR && <div className="pts:text-red">处理失败</div>}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default RAGFileList;
