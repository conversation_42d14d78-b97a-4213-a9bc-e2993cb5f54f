import React from "react";

import { AgentWelcome, PopularQuestion } from "@/components";

interface DefaultAgentLayoutProps {
  navigationBar?: React.ReactNode;
  welcome?: React.ReactNode;
  sender: React.ReactNode;
  popularQuestion?: React.ReactNode;
}

const DefaultAgentLayout: React.FC<DefaultAgentLayoutProps> = (props) => {
  const { sender, welcome, popularQuestion, navigationBar } = props;

  return (
    <div className="pts:flex pts:flex-col pts:bg-[#FBFCFD] pts:w-full pts:h-full cscs-agent-home-container">
      {/* Middle content - adaptive width */}
      <div>{navigationBar}</div>
      <div className="pts:flex-1 pts:h-full">
        <div className="pts:flex pts:flex-col pts:justify-center pts:items-center pts:mt-[140px] pts:mb-8 pts:overflow-y-auto">
          {welcome ? welcome : <AgentWelcome />}
        </div>
        <div className="pts:mx-auto pts:mb-4 pts:pb-4 pts:w-[800px]">{sender}</div>
        <div className="pts:mx-auto pts:w-[800px]">{popularQuestion ? popularQuestion : <PopularQuestion />}</div>
      </div>
      <div className="pts:pb-2 pts:text-black-45 pts:text-sm pts:text-center">内容由 AI 生成，请仔细甄别</div>
    </div>
  );
};

export default DefaultAgentLayout;
