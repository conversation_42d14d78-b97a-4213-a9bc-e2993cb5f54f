import { Form, Input, Modal } from "antd";
import React, { useEffect } from "react";

import Highlight from "../../components/highlight/Highlight";

interface ConversationTitleProps {
  title: string;
  keyword: string;
  onEdited: (title: string) => void;
  editable: boolean;
  onCancel: () => void;
}

const Title: React.FC<ConversationTitleProps> = (props) => {
  const { title, keyword, onEdited, onCancel, editable } = props;
  const [form] = Form.useForm();

  useEffect(() => {
    if (editable) {
      form.setFieldValue("title", title);
    }
  }, [editable, title]);

  const handleOk = async () => {
    const values = await form.validateFields();
    onEdited(values.title);
  };

  if (!editable) {
    return (
      <span className="pts:font-bold pts:text-base">
        <Highlight text={title} keyword={keyword} />
      </span>
    );
  }

  return (
    <span
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      <Modal title="重命名对话" onOk={handleOk} open={editable} onCancel={onCancel}>
        <Form form={form}>
          <Form.Item
            name="title"
            rules={[
              {
                required: true,
                message: "请输入名称",
              },
              {
                max: 15,
                message: "最多15个字",
              },
            ]}
          >
            <Input placeholder="请输入" />
          </Form.Item>
        </Form>
      </Modal>
    </span>
  );
};

export default Title;
