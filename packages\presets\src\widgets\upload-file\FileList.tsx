import { Tooltip, message } from "antd";
import { nanoid } from "nanoid";
import React, { useCallback, useEffect, useRef, useState } from "react";

import { PresetsCommand } from "@/command";
import {
  BuildInCommand,
  StandardResponse,
  getAssetUrl,
  post,
  useCommandRunner,
  useSenderInterceptor,
  useSubscribeCommand,
} from "@cscs-agent/core";

interface FileItem {
  id: string;
  file: File;
  fileId?: string;
  name: string;
  type: string;
  size: string;
  status: "success" | "failed" | "uploading";
  progress?: number; // Upload progress percentage (0-100)
}

enum FileUploadStatus {
  UPLOADING = "uploading",
  PARSING = "parsing",
  READY = "ready",
  ERROR = "error",
}

interface FileUploadResponse {
  id: string;
  file_name: string;
  file_size: number;
  status: FileUploadStatus;
  message: string;
}

const fileTypeIcons: any = {
  word: {
    url: getAssetUrl("/assets/images/<EMAIL>"),
  },
  ppt: {
    url: getAssetUrl("/assets/images/<EMAIL>"),
  },
  excel: {
    url: getAssetUrl("/assets/images/<EMAIL>"),
  },
  pdf: {
    url: getAssetUrl("/assets/images/<EMAIL>"),
  },
  pic: {
    url: getAssetUrl("/assets/images/<EMAIL>"),
  },
  code: {
    url: getAssetUrl("/assets/images/<EMAIL>"),
  },
  common: {
    url: getAssetUrl("/assets/images/<EMAIL>"),
  },
};

const FileList: React.FC = () => {
  const [fileList, setFileList] = useState<FileItem[]>([]);
  const innerRef = useRef<HTMLDivElement | null>(null);
  const wrapperRef = useRef<HTMLDivElement | null>(null);
  const [posX, setPosX] = useState(0);
  const [open, setOpen] = useState(false);
  const runner = useCommandRunner();
  const [showLeftButton, setShowLeftButton] = useState(false);
  const [showRightButton, setShowRightButton] = useState(false);

  useSubscribeCommand(PresetsCommand.UploadFiles, ({ files }) => {
    const newFiles: FileItem[] = [];
    for (const file of files) {
      const localFileId = nanoid();
      // Create FileItem for this upload
      const newFileItem: FileItem = {
        id: localFileId,
        file,
        name: file.name,
        type: getFileType(file.name),
        size: formatFileSize(file.size),
        status: "uploading",
        progress: 0,
      };

      newFiles.push(newFileItem);
    }

    setFileList((prevFiles) => [...prevFiles, ...newFiles]);
    uploadFiles(newFiles);

    setTimeout(() => {
      swipeList("right", true);
    });
  });

  useSubscribeCommand(PresetsCommand.OpenUploadFileList, () => {
    setOpen(true);
  });

  useSubscribeCommand(PresetsCommand.CloseUploadFileList, () => {
    setOpen(false);
  });

  useSubscribeCommand(BuildInCommand.EmitMessageSendedEvent, () => {
    // 消息发送后清空files
    setFileList([]);
    setOpen(false);
  });

  useSenderInterceptor(
    () => {
      if (fileList.some((i) => i.status === "uploading")) {
        return Promise.resolve({ message: "请先等待文件上传完成" });
      }
      return Promise.resolve(null);
    },
    "send",
    [fileList],
  );

  useEffect(() => {
    if (fileList.length === 0) {
      setOpen(false);
    }
    resetPosition();

    const uploadedFiles = fileList.filter((i) => i.status === "success" && i.fileId);
    runner(BuildInCommand.UpdateChatFileIdList, {
      setValue: () => {
        return uploadedFiles.map((i) => i.fileId);
      },
    });
  }, [fileList]);

  // Helper function to determine file type based on file extension
  const getFileType = (fileName: string): string => {
    const extension = fileName.split(".").pop()?.toLowerCase() ?? "unknown";

    const typeMapper: Record<string, string> = {
      doc: "word",
      docx: "word",
      ppt: "ppt",
      pptx: "ppt",
      xls: "excel",
      xlsx: "excel",
      md: "common",
      pdf: "pdf",
      jpg: "pic",
      jpeg: "pic",
      gif: "pic",
      png: "pic",
      json: "code",
      unknown: "common",
    };

    return typeMapper[extension] ?? "common";
  };

  // Helper function to format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  };

  // Enhanced onUploadProgress method
  const onUploadProgress = useCallback((localFileId: string) => {
    return (progressEvent: { loaded: number; total?: number }) => {
      const { loaded, total } = progressEvent;

      if (!total) return;

      // Calculate progress percentage
      const progressPercentage = Math.round((loaded / total) * 100);

      // Update file progress
      setFileList((prevFiles) =>
        prevFiles.map((fileItem) =>
          fileItem.id === localFileId ? { ...fileItem, progress: progressPercentage } : fileItem,
        ),
      );

      // Handle completion
      if (progressPercentage >= 100) {
        // Small delay to show 100% before changing to success
        setTimeout(() => {
          setFileList((prevFiles) =>
            prevFiles.map((fileItem) => (fileItem.id === localFileId ? { ...fileItem, progress: 99 } : fileItem)),
          );
        }, 200);
      }
    };
  }, []);

  const uploadFiles = async (filesToUpload: FileItem[]) => {
    for (const currentFile of filesToUpload) {
      const localFileId = currentFile.id;

      // Prepare form data
      const formData = new FormData();
      formData.append("file", currentFile.file);

      // Start upload with progress tracking
      post<StandardResponse<FileUploadResponse>>("/rag/files/upload", formData, {
        onUploadProgress: onUploadProgress(localFileId),
        headers: {
          "Content-Type": "multipart/form-data",
        },
        timeout: 60000, // 60 second timeout for file uploads
      })
        .then((res) => {
          if (res.data.code === 200) {
            const fileId = res.data.data.id;
            setTimeout(() => {
              setFileList((prevFiles) =>
                prevFiles.map((fileItem) =>
                  fileItem.id === localFileId
                    ? { ...fileItem, fileId, status: "success" as const, progress: undefined }
                    : fileItem,
                ),
              );
            }, 200);
          }
        })
        .catch((error) => {
          // Handle upload error
          console.error("Upload failed:", error);
          setFileList((prevFiles) =>
            prevFiles.map((fileItem) =>
              fileItem.id === localFileId ? { ...fileItem, status: "failed" as const, progress: undefined } : fileItem,
            ),
          );
          message.error(error.message);
        });
    }
  };

  const removeFile = (id: string) => {
    setFileList((prevFiles) => prevFiles.filter((file) => file.id !== id));
  };

  const swipeList = (direction: "left" | "right", toEdge?: boolean) => {
    const step = 400;
    const innerEle = innerRef.current;
    const wrapperEle = wrapperRef.current;
    const innerWidth = innerEle?.scrollWidth ?? 0;
    const wrapperWidth = wrapperEle?.getBoundingClientRect().width ?? 0;

    let newPosX = direction === "left" ? posX + step : posX - step;

    if (toEdge) {
      newPosX = direction === "left" ? 0 : wrapperWidth - innerWidth;
    }

    if (newPosX < wrapperWidth - innerWidth) {
      newPosX = wrapperWidth - innerWidth;
    }

    if (newPosX > 0) {
      newPosX = 0;
    }

    if (innerEle) {
      innerEle.style.transform = `translateX(${newPosX}px)`;
    }

    setPosX(newPosX);

    if (newPosX < 0) {
      setShowLeftButton(true);
    } else {
      setShowLeftButton(false);
    }

    if (innerWidth + newPosX > wrapperWidth) {
      setShowRightButton(true);
    } else {
      setShowRightButton(false);
    }
  };

  const resetPosition = () => {
    const innerEle = innerRef.current;
    const wrapperEle = wrapperRef.current;
    const innerWidth = innerEle?.scrollWidth ?? 0;
    const wrapperWidth = wrapperEle?.getBoundingClientRect().width ?? 0;

    const posX = Number(innerEle?.style.transform.replace("translateX(", "").replace("px)", "") ?? 0);

    if (posX + innerWidth < wrapperWidth) {
      swipeList("right", true);
    }
  };

  return (
    <div
      className="pts:relative pts:pt-2 pts:pb-3 pts:border-b pts:border-b-[rgba(0,0,0,0.09)] pts:w-full pts:max-w-4xl"
      hidden={!open}
    >
      {/* File Upload Container */}
      <div className="pts:relative">
        <button
          hidden={!showLeftButton}
          onClick={() => swipeList("left")}
          className="pts:top-[30px] pts:left-0 pts:z-20 pts:absolute pts:flex pts:justify-center pts:items-center pts:bg-white pts:hover:bg-gray-50 pts:border pts:border-[rgba(0,0,0,0.09)] pts:rounded-full pts:w-6 pts:h-6 pts:transition-colors pts:cursor-pointer"
        >
          <svg className="pts:w-4 pts:h-4 pts:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <div
          hidden={!showLeftButton}
          className="pts:top-0 pts:left-0 pts:z-10 pts:absolute pts:bg-gradient-to-r pts:from-white pts:to-[rgba(255,255,255,0)] pts:w-[52px] pts:h-[76px]"
        ></div>

        {/* File Items */}
        <div className="pts:pt-2 pts:overflow-hidden" ref={wrapperRef}>
          <div className="pts:whitespace-nowrap pts:transition-all pts:translate-x-0" ref={innerRef}>
            {fileList.map((file) => {
              const iconUrl = fileTypeIcons[file.type].url;

              return (
                <div
                  key={file.id}
                  className="pts:inline-block pts:relative pts:bg-gray-50 pts:hover:bg-gray-100 pts:mr-2 pts:p-4 pts:rounded-lg pts:w-[196px] pts:h-[68px] pts:transition-colors pts:cursor-pointer"
                >
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      removeFile(file.id);
                    }}
                    className="pts:-top-2 pts:-right-2 pts:absolute pts:flex pts:justify-center pts:items-center pts:bg-gray-400 pts:hover:bg-gray-500 pts:p-0 pts:border-0 pts:rounded-full pts:w-4 pts:h-4 pts:hover:scale-100 pts:scale-75 pts:transition-colors pts:cursor-pointer"
                  >
                    <svg
                      className="pts:w-3 pts:h-3 pts:text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>

                  {/* File Icon and Info */}
                  <div className="pts:flex pts:items-center pts:gap-3 pts:mb-2">
                    <div className="pts:flex pts:justify-center pts:items-center pts:rounded-lg pts:w-10 pts:h-10">
                      <span className="pts:font-bold pts:text-sm">
                        <img src={iconUrl} width="36" />
                      </span>
                    </div>
                    <div className="pts:flex-1 pts:min-w-0">
                      <Tooltip title={file.name}>
                        <div className="pts:font-medium pts:text-gray-900 pts:text-sm pts:truncate">{file.name}</div>
                      </Tooltip>
                      <div className="pts:flex pts:items-center pts:gap-2 pts:mt-1">
                        {file.status === "uploading" ? (
                          <span className="pts:font-medium pts:text-blue-500 pts:text-xs">
                            上传中 ... {Math.round(file.progress || 0)}%
                          </span>
                        ) : (
                          <span className="pts:text-gray-500 pts:text-xs">
                            {file.type === "markdown" ? "md" : file.size}
                          </span>
                        )}
                        {file.status === "failed" && (
                          <span className="pts:font-medium pts:text-red-500 pts:text-xs">上传失败</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        <div
          hidden={!showRightButton}
          className="pts:top-0 pts:right-0 pts:z-10 pts:absolute pts:bg-gradient-to-l pts:from-white pts:to-[rgba(255,255,255,0)] pts:w-[52px] pts:h-[76px]"
        ></div>

        <button
          hidden={!showRightButton}
          onClick={() => swipeList("right")}
          className="pts:top-[30px] pts:right-0 pts:z-20 pts:absolute pts:flex pts:justify-center pts:items-center pts:bg-white pts:hover:bg-gray-50 pts:border pts:border-[rgba(0,0,0,0.09)] pts:rounded-full pts:w-6 pts:h-6 pts:transition-colors pts:cursor-pointer"
        >
          <svg className="pts:w-4 pts:h-4 pts:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default FileList;
