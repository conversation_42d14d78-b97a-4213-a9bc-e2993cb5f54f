import React from "react";
import { But<PERSON> } from "antd";
import { SearchOutlined, SortAscendingOutlined } from "@ant-design/icons";
import { dynamicPageConfigFactory, dynamicPageQaAgentConfig, nl2sqlAgentConfig } from "@cscs-agent/agents";
import { Role, type AgentChatConfig } from "@cscs-agent/core";
import {
  Copy,
  PromptTemplate,
  Rating,
  ThoughtChain,
  Message,
  Regenerate,
  FileList,
  UploadButton,
  RAGFileList,
} from "@cscs-agent/presets";

import InsertTag from "./widgets/insert-tag";
import InsertText from "./widgets/insert-text";
import FormDemo from "./widgets/form";
import OpenSidePanelMessage from "./widgets/open-side-panel-message";
import AddExtendData from "./widgets/extend-data";
import AppendWidget from "./widgets/append-widget";

const dynamicPageAgentConfig = dynamicPageConfigFactory({
  saveApiUrl: () => {
    return `/gateway/cluster/page/system/dynamic/page/manage/savePageForAi`;
  },
  previewUrl: (id: string) => `http://172.17.6.116:8002/dynamic_page/agent_preview/${id}`,
});

export const config: AgentChatConfig = {
  agents: [
    dynamicPageAgentConfig,
    dynamicPageQaAgentConfig,
    nl2sqlAgentConfig,
    {
      name: "测试智能体",
      code: "test",
      message: {
        blocks: {
          widgets: [
            {
              code: "@DynamicPage/PreviewButton",
              component: () => {
                return <Button>PreviewWidget</Button>;
              },
            },
            {
              code: "@BuildIn/ThoughtChain",
              component: ThoughtChain,
            },
            {
              code: "@Test/FormDemo",
              component: FormDemo,
            },
            {
              code: "@BuildIn/Message",
              component: Message,
            },
            {
              code: "@Test/OpenSidePanelMessage",
              component: OpenSidePanelMessage,
            },
            {
              code: "@BuildIn/RAGFileList",
              component: RAGFileList,
            },
          ],
        },
        slots: {
          footer: {
            widgets: [
              {
                code: "Copy",
                component: Copy,
                role: Role.AI,
              },
              {
                code: "Rating",
                component: Rating,
                role: Role.AI,
              },
              {
                code: "Regenerate",
                component: Regenerate,
                role: Role.AI,
              },
            ],
          },
          append: {
            widgets: [
              {
                code: "AppendWidget",
                component: AppendWidget,
                role: Role.HUMAN,
              },
            ],
          },
        },
      },
      prompts: [
        {
          icon: <SearchOutlined />,
          title: "字段查询字段查询字段查询",
          description: "根据字段查询列表",
          prompt: "请查询{{[企业名称]}}获取企业信息",
        },
        {
          icon: <SortAscendingOutlined />,
          title: "排序",
          description: "配置列表排序",
          prompt: "请对列表进行排序",
        },
      ],
      commands: [
        {
          name: "",
          action: () => {},
        },
      ],
      suggestions: [],
      sender: {
        slots: {
          headerPanel: {
            widgets: [
              {
                code: "PromptTemplate",
                component: PromptTemplate,
              },
            ],
          },
          header: {
            widgets: [
              {
                code: "FileList",
                component: FileList,
                placement: "inside",
              },
              {
                code: "InsertText",
                component: InsertText,
                placement: "outside",
              },
              {
                code: "InsertTag",
                component: InsertTag,
                placement: "outside",
              },
            ],
          },
          footer: {
            widgets: [
              {
                code: "UploadButton",
                component: UploadButton,
                placement: "inside",
                props: {
                  accept: [
                    ".doc",
                    ".docx",
                    ".ppt",
                    ".pptx",
                    ".xls",
                    ".xlsx",
                    ".pdf",
                    ".md",
                    ".txt",
                    ".jpg",
                    ".jpeg",
                    ".gif",
                    ".png",
                    ".sql",
                  ],
                },
              },
              {
                code: "AddExtendData",
                component: AddExtendData,
                placement: "inside",
              },
            ],
          },
        },
      },
      sidePanel: {
        render: {
          widgets: [
            {
              code: "@BuildIn/Message",
              component: Message,
            },
          ],
        },
      },
      request: {
        chat: {
          url: "/chat/completion",
          headers: {},
          method: "POST",
        },
      },
    },
  ],
};
