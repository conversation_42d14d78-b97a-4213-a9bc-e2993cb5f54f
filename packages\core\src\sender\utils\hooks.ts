/* eslint-disable react-hooks/exhaustive-deps */
import { nanoid } from "nanoid";
import { useContext, useEffect, useRef } from "react";

import { SenderInterceptor } from "@/types";

import { SenderContext } from "./context";

export function useSenderInterceptor(
  interceptor: SenderInterceptor["interceptor"],
  type: string = "send",
  deps: any[] = [],
) {
  const { setInterceptors } = useContext(SenderContext);
  const idRef = useRef(nanoid());

  useEffect(() => {
    setInterceptors([
      {
        id: idRef.current,
        type,
        interceptor,
      },
    ]);
  }, [...deps]);
}
